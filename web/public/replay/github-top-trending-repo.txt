event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "id": "run-e17aa6cd-dc71-4748-853a-1ae14dde2d56", "role": "user", "content": "Write a brief on the top 1 trending repo on Github today.", "finish_reason": "stop"}

event: tool_calls
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "coordinator", "id": "run-baa8d67c-5aef-4bca-b2f5-03eb8c6849d7", "role": "assistant", "finish_reason": "tool_calls", "tool_calls": [{"name": "handoff_to_planner", "args": {"task_title": "Write a brief on the top 1 trending repo on Github today"}, "id": "", "type": "tool_call"}], "tool_call_chunks": [{"name": "handoff_to_planner", "args": "{\"task_title\":\"Write a brief on the top 1 trending repo on Github today\"}", "id": "", "index": null, "type": "tool_call_chunk"}]}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": "{"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": "\n  \""}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": "locale\": \"en-US\",\n  \"has_enough_context\":"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": " false,\n  \"thought\": \"To provide a brief on the top "}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": "1 trending repository on GitHub today, I need to gather real-time information about GitHub's trending repositories. This includes the repository's name, owner, description, the"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": " reason it's trending, and some key statistics (stars, forks, contributors, etc.).\",\n  \"title\": \"Research Plan: Top Trending GitHub Repository Today"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": "\",\n  \"steps\": [\n    {\n      \"need_web_search\": true,\n      \"title\": \"Identify and Profile the Top Trending Repository\",\n      \"description\": \"Identify the #1 trending repository on"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": " GitHub today. Collect the following information: repository name, repository owner/organization, a short description of the repository's purpose, the primary programming language used, and the reason GitHub marks it as trending (e.g., 'X new stars today"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": "'). Note: ensure to filter for 'today' to get the current trending repo.\",\n      \"step_type\": \"research\"\n    },\n    {\n      \"need_web_search\": true,\n      \"title\": \"Gather Repository Statistics and Community Data\",\n      \"description\": \"Collect"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": " detailed statistics for the top trending repository. This includes the total number of stars, forks, open issues, closed issues, contributors, and recent commit activity. Also, gather data about the community's involvement, such as the number of active contributors in the last month, and any available information on significant discussions or contributions happening"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": " within the project. Check for recent release notes or announcements.\",\n      \"step_type\": \"research\"\n    },\n    {\n      \"need_web_search\": true,\n      \"title\": \"Determine Context and Significance\",\n      \"description\": \"Research the broader context and significance of the trending"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": " repository. Determine the repository's purpose or function. Investigate the project's background, the problem it solves, or the features it provides. Identify the industry, user base, or application area it serves. Search for recent news, articles, or blog posts mentioning the repository and its impact or potential. Identify its license"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant", "content": " and understand the terms of use and distribution.\",\n      \"step_type\": \"research\"\n    }\n  ]\n}", "finish_reason": "stop"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "planner", "id": "run-33af75e6-c1b5-4276-9749-7cfb7a967402", "role": "assistant"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a26e53fe-9771-42ec-a302-5cbcff840823", "role": "assistant", "content": "# Problem Statement\n"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a26e53fe-9771-42ec-a302-5cbcff840823", "role": "assistant", "content": "The task is to identify the top trending repository on GitHub today, and collect its"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a26e53fe-9771-42ec-a302-5cbcff840823", "role": "assistant", "content": " name, owner, description, primary language, and the reason it's trending"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a26e53fe-9771-42ec-a302-5cbcff840823", "role": "assistant", "content": ". The search should be filtered to show only repositories trending today.\n\n# Research Findings\n\n### Top Trending Repository on GitHub\nI will use the `get_"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a26e53fe-9771-42ec-a302-5cbcff840823", "role": "assistant", "content": "github_trending_repositories` tool to find the top trending repository on GitHub for today.\n\n"}

event: tool_calls
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a26e53fe-9771-42ec-a302-5cbcff840823", "role": "assistant", "finish_reason": "tool_calls", "tool_calls": [{"name": "get_github_trending_repositories", "args": {"since": "daily"}, "id": "", "type": "tool_call"}], "tool_call_chunks": [{"name": "get_github_trending_repositories", "args": "{\"since\":\"daily\"}", "id": "", "index": null, "type": "tool_call_chunk"}]}

event: tool_call_result
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "153737f0-955b-46de-ac03-0df11c189f1b", "role": "assistant", "content": "[\n  {\n    \"name\": \"suna\",\n    \"fullname\": \"kortix-ai/suna\",\n    \"url\": \"https://github.com/kortix-ai/suna\",\n    \"description\": \"\\n      Suna - Open Source Generalist AI Agent\\n    \",\n    \"language\": \"TypeScript\",\n    \"stars\": 4350,\n    \"forks\": 497,\n    \"current_period_stars\": 1105\n  },\n  {\n    \"name\": \"GPT-SoVITS\",\n    \"fullname\": \"RVC-Boss/GPT-SoVITS\",\n    \"url\": \"https://github.com/RVC-Boss/GPT-SoVITS\",\n    \"description\": \"\\n      1 min voice data can also be used to train a good TTS model! (few shot voice cloning)\\n    \",\n    \"language\": \"Python\",\n    \"stars\": 45237,\n    \"forks\": 5011,\n    \"current_period_stars\": 128\n  },\n  {\n    \"name\": \"Free-Certifications\",\n    \"fullname\": \"cloudcommunity/Free-Certifications\",\n    \"url\": \"https://github.com/cloudcommunity/Free-Certifications\",\n    \"description\": \"\\n      A curated list of free courses with certifications. Also available at https://free-certifications.com/\\n\",\n    \"language\": \"\",\n    \"stars\": 31414,\n    \"forks\": 2248,\n    \"current_period_stars\": 894\n  },\n  {\n    \"name\": \"UI-TARS\",\n    \"fullname\": \"bytedance/UI-TARS\",\n    \"url\": \"https://github.com/bytedance/UI-TARS\",\n    \"description\": \"\",\n    \"language\": \"Python\",\n    \"stars\": 5024,\n    \"forks\": 340,\n    \"current_period_stars\": 410\n  },\n  {\n    \"name\": \"generative-ai-for-beginners\",\n    \"fullname\": \"microsoft/generative-ai-for-beginners\",\n    \"url\": \"https://github.com/microsoft/generative-ai-for-beginners\",\n    \"description\": \"\\n      21 Lessons, Get Started Building with Generative AI \\ud83d\\udd17 https://microsoft.github.io/generative-ai-for-beginners/\\n\",\n    \"language\": \"Jupyter Notebook\",\n    \"stars\": 79617,\n    \"forks\": 41214,\n    \"current_period_stars\": 212\n  },\n  {\n    \"name\": \"leaked-system-prompts\",\n    \"fullname\": \"jujumilk3/leaked-system-prompts\",\n    \"url\": \"https://github.com/jujumilk3/leaked-system-prompts\",\n    \"description\": \"\\n      Collection of leaked system prompts\\n    \",\n    \"language\": \"\",\n    \"stars\": 3048,\n    \"forks\": 401,\n    \"current_period_stars\": 55\n  },\n  {\n    \"name\": \"khoj\",\n    \"fullname\": \"khoj-ai/khoj\",\n    \"url\": \"https://github.com/khoj-ai/khoj\",\n    \"description\": \"\\n      Your AI second brain. Self-hostable. Get answers from the web or your docs. Build custom agents, schedule automations, do deep research. Turn any online or local LLM into your personal, autonomous AI (gpt, claude, gemini, llama, qwen, mistral). Get started - free.\\n    \",\n    \"language\": \"Python\",\n    \"stars\": 29113,\n    \"forks\": 1622,\n    \"current_period_stars\": 42\n  },\n  {\n    \"name\": \"dinov2\",\n    \"fullname\": \"facebookresearch/dinov2\",\n    \"url\": \"https://github.com/facebookresearch/dinov2\",\n    \"description\": \"\\n      PyTorch code and models for the DINOv2 self-supervised learning method.\\n    \",\n    \"language\": \"Jupyter Notebook\",\n    \"stars\": 10433,\n    \"forks\": 940,\n    \"current_period_stars\": 8\n  },\n  {\n    \"name\": \"tensorflow\",\n    \"fullname\": \"tensorflow/tensorflow\",\n    \"url\": \"https://github.com/tensorflow/tensorflow\",\n    \"description\": \"\\n      An Open Source Machine Learning Framework for Everyone\\n    \",\n    \"language\": \"C++\",\n    \"stars\": 189614,\n    \"forks\": 74652,\n    \"current_period_stars\": 43\n  }\n]", "tool_call_id": ""}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-c4463d4a-9040-47c8-b996-9c14da32f93b", "role": "assistant", "content": "Based"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-c4463d4a-9040-47c8-b996-9c14da32f93b", "role": "assistant", "content": " on the `get_github_trending_repositories` tool, the top trending repository"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-c4463d4a-9040-47c8-b996-9c14da32f93b", "role": "assistant", "content": " on GitHub today is `kortix-ai/suna`.\n\n*   **"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-c4463d4a-9040-47c8-b996-9c14da32f93b", "role": "assistant", "content": "Repository Name:** suna\n*   **Repository Owner/Organization:** kortix-ai\n*   **Description:** Suna - Open Source Generalist AI Agent\n*   "}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-c4463d4a-9040-47c8-b996-9c14da32f93b", "role": "assistant", "content": "**Primary Language:** TypeScript\n*   **Trending Reason:** 1105 new stars today\n\n# Conclusion\n\nThe #1 trending repository on GitHub today is "}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-c4463d4a-9040-47c8-b996-9c14da32f93b", "role": "assistant", "content": "`kortix-ai/suna`, an Open Source Generalist AI Agent written in TypeScript, with 1105 new stars today.\n\n# References\n\n- [Trending Repositories on GitHub](https://github.com/"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-c4463d4a-9040-47c8-b996-9c14da32f93b", "role": "assistant", "content": "kortix-ai/suna)\n", "finish_reason": "stop"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2471d981-f520-4852-bf19-29b3da5b7455", "role": "assistant", "content": "Problem Statement: Collect"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2471d981-f520-4852-bf19-29b3da5b7455", "role": "assistant", "content": " detailed statistics for the top trending repository, including the total number of stars, forks"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2471d981-f520-4852-bf19-29b3da5b7455", "role": "assistant", "content": ", open issues, closed issues, contributors, and recent commit activity. Also,"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2471d981-f520-4852-bf19-29b3da5b7455", "role": "assistant", "content": " gather data about the community's involvement, such as the number of active contributors in the last month, and any available information on significant discussions or contributions happening within"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2471d981-f520-4852-bf19-29b3da5b7455", "role": "assistant", "content": " the project. Check for recent release notes or announcements.\n\nResearch Findings:\n\nI will start by identifying the top trending repository. I will focus on repositories without"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2471d981-f520-4852-bf19-29b3da5b7455", "role": "assistant", "content": " specifying a language to get the overall top trending repository. Then, I will gather statistics and community data for the identified repository.\n\n"}

event: tool_calls
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2471d981-f520-4852-bf19-29b3da5b7455", "role": "assistant", "finish_reason": "tool_calls", "tool_calls": [{"name": "get_github_trending_repositories", "args": {}, "id": "", "type": "tool_call"}], "tool_call_chunks": [{"name": "get_github_trending_repositories", "args": "{}", "id": "", "index": null, "type": "tool_call_chunk"}]}

event: tool_call_result
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "6970563c-5f52-4098-b6da-7fdcd0e4653c", "role": "assistant", "content": "[\n  {\n    \"name\": \"suna\",\n    \"fullname\": \"kortix-ai/suna\",\n    \"url\": \"https://github.com/kortix-ai/suna\",\n    \"description\": \"\\n      Suna - Open Source Generalist AI Agent\\n    \",\n    \"language\": \"TypeScript\",\n    \"stars\": 4350,\n    \"forks\": 497,\n    \"current_period_stars\": 1105\n  },\n  {\n    \"name\": \"GPT-SoVITS\",\n    \"fullname\": \"RVC-Boss/GPT-SoVITS\",\n    \"url\": \"https://github.com/RVC-Boss/GPT-SoVITS\",\n    \"description\": \"\\n      1 min voice data can also be used to train a good TTS model! (few shot voice cloning)\\n    \",\n    \"language\": \"Python\",\n    \"stars\": 45237,\n    \"forks\": 5011,\n    \"current_period_stars\": 128\n  },\n  {\n    \"name\": \"Free-Certifications\",\n    \"fullname\": \"cloudcommunity/Free-Certifications\",\n    \"url\": \"https://github.com/cloudcommunity/Free-Certifications\",\n    \"description\": \"\\n      A curated list of free courses with certifications. Also available at https://free-certifications.com/\\n\",\n    \"language\": \"\",\n    \"stars\": 31414,\n    \"forks\": 2248,\n    \"current_period_stars\": 894\n  },\n  {\n    \"name\": \"UI-TARS\",\n    \"fullname\": \"bytedance/UI-TARS\",\n    \"url\": \"https://github.com/bytedance/UI-TARS\",\n    \"description\": \"\",\n    \"language\": \"Python\",\n    \"stars\": 5024,\n    \"forks\": 340,\n    \"current_period_stars\": 410\n  },\n  {\n    \"name\": \"generative-ai-for-beginners\",\n    \"fullname\": \"microsoft/generative-ai-for-beginners\",\n    \"url\": \"https://github.com/microsoft/generative-ai-for-beginners\",\n    \"description\": \"\\n      21 Lessons, Get Started Building with Generative AI \\ud83d\\udd17 https://microsoft.github.io/generative-ai-for-beginners/\\n\",\n    \"language\": \"Jupyter Notebook\",\n    \"stars\": 79617,\n    \"forks\": 41214,\n    \"current_period_stars\": 212\n  },\n  {\n    \"name\": \"leaked-system-prompts\",\n    \"fullname\": \"jujumilk3/leaked-system-prompts\",\n    \"url\": \"https://github.com/jujumilk3/leaked-system-prompts\",\n    \"description\": \"\\n      Collection of leaked system prompts\\n    \",\n    \"language\": \"\",\n    \"stars\": 3048,\n    \"forks\": 401,\n    \"current_period_stars\": 55\n  },\n  {\n    \"name\": \"khoj\",\n    \"fullname\": \"khoj-ai/khoj\",\n    \"url\": \"https://github.com/khoj-ai/khoj\",\n    \"description\": \"\\n      Your AI second brain. Self-hostable. Get answers from the web or your docs. Build custom agents, schedule automations, do deep research. Turn any online or local LLM into your personal, autonomous AI (gpt, claude, gemini, llama, qwen, mistral). Get started - free.\\n    \",\n    \"language\": \"Python\",\n    \"stars\": 29113,\n    \"forks\": 1622,\n    \"current_period_stars\": 42\n  },\n  {\n    \"name\": \"dinov2\",\n    \"fullname\": \"facebookresearch/dinov2\",\n    \"url\": \"https://github.com/facebookresearch/dinov2\",\n    \"description\": \"\\n      PyTorch code and models for the DINOv2 self-supervised learning method.\\n    \",\n    \"language\": \"Jupyter Notebook\",\n    \"stars\": 10433,\n    \"forks\": 940,\n    \"current_period_stars\": 8\n  },\n  {\n    \"name\": \"tensorflow\",\n    \"fullname\": \"tensorflow/tensorflow\",\n    \"url\": \"https://github.com/tensorflow/tensorflow\",\n    \"description\": \"\\n      An Open Source Machine Learning Framework for Everyone\\n    \",\n    \"language\": \"C++\",\n    \"stars\": 189614,\n    \"forks\": 74652,\n    \"current_period_stars\": 43\n  }\n]", "tool_call_id": ""}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a13d3d39-db41-40d9-a066-449f4bfeb1ad", "role": "assistant", "content": "Based"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a13d3d39-db41-40d9-a066-449f4bfeb1ad", "role": "assistant", "content": " on the trending repositories, \"kortix-ai/suna\" appears to be a"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a13d3d39-db41-40d9-a066-449f4bfeb1ad", "role": "assistant", "content": " rapidly trending repository.\n\nRepository Statistics for kortix-ai/suna:\n"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a13d3d39-db41-40d9-a066-449f4bfeb1ad", "role": "assistant", "content": "*   Name: suna\n*   Full Name: kortix-ai/suna\n*   URL: [https://github.com/kortix"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a13d3d39-db41-40d9-a066-449f4bfeb1ad", "role": "assistant", "content": "-ai/suna](https://github.com/kortix-ai/suna)\n*   Description: Suna - Open Source Generalist"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a13d3d39-db41-40d9-a066-449f4bfeb1ad", "role": "assistant", "content": " AI Agent\n*   Language: TypeScript\n*   Stars: 4350\n*   Forks: 497\n*   Current Period Stars: 1105\n\nTo gather more detailed statistics, I"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a13d3d39-db41-40d9-a066-449f4bfeb1ad", "role": "assistant", "content": " will crawl the repository URL.\n\n"}

event: tool_calls
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-a13d3d39-db41-40d9-a066-449f4bfeb1ad", "role": "assistant", "finish_reason": "tool_calls", "tool_calls": [{"name": "crawl_tool", "args": {"url": "https://github.com/kortix-ai/suna"}, "id": "", "type": "tool_call"}], "tool_call_chunks": [{"name": "crawl_tool", "args": "{\"url\":\"https://github.com/kortix-ai/suna\"}", "id": "", "index": null, "type": "tool_call_chunk"}]}

event: tool_call_result
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "20c87525-b653-4563-9afb-************", "role": "assistant", "content": "{\"url\": \"https://github.com/kortix-ai/suna\", \"crawled_content\": \"# None\\n\\n{\\\"data\\\":null,\\\"code\\\":402,\\\"name\\\":\\\"InsufficientBalanceError\\\",\\\"status\\\":40203,\\\"message\\\":\\\"Account balance not enough to run this query, please recharge.\\\",\\\"readableMessage\\\":\\\"InsufficientBalanceError: Account balance not enough to run this query, please recharge.\\\"}\"}", "tool_call_id": ""}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-824b28eb-3885-433d-9073-779881132dec", "role": "assistant", "content": "I"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-824b28eb-3885-433d-9073-779881132dec", "role": "assistant", "content": " encountered an error while trying to crawl the Suna repository page due to insufficient balance."}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-824b28eb-3885-433d-9073-779881132dec", "role": "assistant", "content": " I will proceed using the available information from the trending repositories API to conduct web searches for additional"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-824b28eb-3885-433d-9073-779881132dec", "role": "assistant", "content": " information.\n\nI'll conduct a web search to gather further details on the \"kortix-ai/suna\" repository, including its community involvement,"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-824b28eb-3885-433d-9073-779881132dec", "role": "assistant", "content": " recent releases, and discussions.\n\n"}

event: tool_calls
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-824b28eb-3885-433d-9073-779881132dec", "role": "assistant", "finish_reason": "tool_calls", "tool_calls": [{"name": "web_search", "args": {"query": "kortix-ai/suna community activity releases discussions"}, "id": "", "type": "tool_call"}], "tool_call_chunks": [{"name": "web_search", "args": "{\"query\":\"kortix-ai/suna community activity releases discussions\"}", "id": "", "index": null, "type": "tool_call_chunk"}]}

event: tool_call_result
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "a1d144a4-30e1-4887-9624-7e3ef5adb13c", "role": "assistant", "content": "[{\"type\": \"page\", \"title\": \"Activity · kortix-ai/suna - GitHub\", \"url\": \"https://github.com/kortix-ai/suna/activity\", \"content\": \"Suna - Open Source Generalist AI Agent. Contribute to kortix-ai/suna development by creating an account on GitHub.\", \"score\": 0.6601948}, {\"type\": \"page\", \"title\": \"Suna: Intelligent Agent with Integrated Browser Operations and Data ...\", \"url\": \"https://www.aisharenet.com/en/suna/\", \"content\": \"Suna is an open source general-purpose AI agent developed by Kortix AI, hosted on GitHub, based on the Apache 2.0 license, allowing users to download, modify and self-host it for free. It helps users with complex tasks such as web browsing, file management, data crawling, and website deployment through natural language conversations.Suna is built on a modular architecture that includes a Python/FastAPI backend, a Next.js/React frontend, a Supabase database, and a Daytona sandbox to ensure\", \"score\": 0.********, \"raw_content\": \"Suna: Intelligent Agents for Integrated Browser Operations and Data Analytics\\n\\nGeneral Introduction\\n\\nSuna is an open source general-purpose AI agent developed by Kortix AI, hosted on GitHub, based on the Apache 2.0 license, allowing users to download, modify and self-host it for free. It helps users with complex tasks such as web browsing, file management, data crawling, and website deployment through natural language conversations.Suna is built on a modular architecture that includes a Python/FastAPI backend, a Next.js/React frontend, a Supabase database, and a Daytona sandbox to ensure security and flexibility. It supports integration with large language models such as OpenAI, Anthropic, and more, with Tavily and RapidAPI provide real-time search and data services.\\n\\n\\n\\n\\n\\nFunction List\\n\\n\\n\\nUsing Help\\n\\nInstallation process\\n\\nSuna supports self-hosting and is suitable for users with Python and Node.js development experience. Here are the detailed installation steps:\\n\\nMain Functions\\n\\nBrowser Automation\\n\\nSuna uses the Chromium browser to perform web actions. For example, type \\\"Extract iPhone 14 price from Amazon\\\":\\n\\nDocument management\\n\\nSuna supports file creation and editing. For example, \\\"Create report.txt to write sales data\\\":\\n\\nWeb crawling and searching\\n\\nSuna integrates with Tavily to provide real-time search. For example, \\\"Analyzing the German automotive market\\\":\\n\\ncommand-line operation\\n\\nSuna runs Linux commands in the Daytona sandbox. For example, \\\"list catalog files\\\":\\n\\nWebsite deployment\\n\\nSuna supports static website deployment. For example, \\\"Deploy my HTML project\\\":\\n\\nFeatured Function Operation\\n\\nMission planning\\n\\nSuna use todo.md Systematically break down tasks. For example, \\\"Plan a 10-day trip to London.\\\"\\n\\nAPI Integration\\n\\nSuna supports APIs for LinkedIn, Zillow, etc. e.g. \\\"Grab Barcelona company data\\\":\\n\\n\\n\\napplication scenario\\n\\n\\n\\nQA\\n\\nRelated articles\\n\\nRecommended\\n\\nCan't find AI tools? Try here!\\n\\nJust type in the keyword Accessibility Bing SearchYou can quickly find all the AI tools on this site.\\n\\nAI Programming IDE\\n\\nAI Digital Human Assistant\\n\\nIntelligent Writing Tools\\n\\nAI Video Generation\\n\\nE-commerce Poster Generation\\n\\nSmart PPT Tools\\n\\nFLUX.1 image generator (supports Chinese input)\\n\\nRecent AI Hotspots\\n\\nAI Tools Recommendations\\n\\nAI Tools Classification\\n\\n© 2022-2025   Chief AI Sharing Circle   \\n\\nAI News\\nAI tools\\nAI utility commands\\nAI hands-on tutorials\\nAI knowledge\\n\\n\\nSite Map | 京ICP备**********号-1\\n\\nAI News\\n\\nAI tools\\n\\nAI utility commands\\n\\nAI hands-on tutorials\\n\\nAI knowledge\\n\\nSite Map | 京ICP备**********号-1\\n\\n\"}, {\"type\": \"page\", \"title\": \"Kortix AI has launched Suna, the world's first open-source general AI ...\", \"url\": \"https://www.news9live.com/technology/artificial-intelligence/kortix-suna-open-source-ai-agent-launched-2842042\", \"content\": \"Artificial Intelligence News\\nKortix AI has launched Suna, the world’s first open-source general AI agent\\nKortix AI has unveiled Suna, the world’s first open-source general AI agent. Built for real-world tasks, it can automate research, write reports, run commands, and more — all from a chat-like interface. It works offline, is free to use, and is now available to host privately.\\n\\nSiddharth Shankar Published: 23 Apr 2025 13:22:PM [...] The project runs on Apache 2.0 license, meaning it’s open for forking, tweaking, or deploying for internal company use.\\nThe vision behind Suna\\nKortix AI says Suna is built for “real-world tasks through natural conversation.” While OpenAI, Google, and others work on tightly-guarded AGI labs, this launch feels like a response from the indie world — showing that open AI agents aren’t just possible, they’re already here. [...] Published Time: 2025-04-23T13:22:53+05:30\\nKortix AI has launched Suna, the world’s first open-source general AI agent | Artificial Intelligence News - News9live\\n\\n\\nHome\\nLatest\\nIndia\\nAnalysis\\nLifeStyle\\nSports\\nEntertainment\\nCities\\nTech\\nBiz\\nHealth\\nVideos\\n\\nLIVE TV\\nmore\\nTrending\", \"score\": 0.5849357, \"raw_content\": \"Published Time: 2025-04-23T13:22:53+05:30\\nKortix AI has launched Suna, the world’s first open-source general AI agent | Artificial Intelligence News - News9live\\n\\n\\nHome\\nLatest\\nIndia\\nAnalysis\\nLifeStyle\\nSports\\nEntertainment\\nCities\\nTech\\nBiz\\nHealth\\nVideos\\n\\nLIVE TV\\nmore\\nTrending\\n\\nLIVE TV\\nIPL 2025\\nCBSE\\nIndian Tigers and Tigresses\\nEx-Karnataka DGP Death\\nCanada Gurdwara Vandalism\\nKashmir Valley School Closed\\nAbhinav Shukla Threat\\nHoroscope Today\\nArtificial Intelligence\\nHindu Calendar 2025\\nMalayalam Calendar 2025\\nMarathi Calendar 2025\\n\\nJoin News9 on WhatsApp\\n\\n\\nState\\n\\nAndhra Pradesh\\nKarnataka\\nKerala\\nTamil Nadu\\nTelangana\\n\\n\\nTechnology\\nGaming\\nMobile Phones\\nProduct Review\\nTech News\\nFeatures\\n\\n\\nEducation / Career\\nBoard Results\\nEntrance Exams\\nExam Results\\nJobs\\nSchool\\n\\n\\nHealth\\nHealth Conditions\\nMedicine\\nMental Health\\nHealth News\\n\\n\\nSports\\nCricket\\nOlympics\\nUFC\\nWWE\\nKabaddi\\nWomen's Premier League\\nFootball\\nOther Sports\\nTennis\\n\\n\\nBusiness\\nCompanies\\nEconomy\\nMarkets\\nPersonal Finance\\nNews\\n\\n\\nCity\\nBengaluru\\nDelhi\\nHyderabad\\nMumbai\\n\\n\\nEntertainment\\nBigg Boss\\nBollywood\\nHollywood\\nMovie Reviews\\nOTT\\nRegional Cinema\\nTelevision\\nTelugu cinema\\nKannada cinema\\nMalayalam cinema\\nTamil cinema\\nCelebs\\n\\n\\nLifestyle\\nBeauty\\nFashion\\nBook Review\\nFood & Drink\\nRelationships\\nSpirituality\\nTravel\\nFitness\\nEvents\\nHome & Garden\\nPets\\n\\n\\nWeb Stories\\nAstro\\nAuto\\nBusiness\\nCity\\nEducation and Career\\nGaming\\nHealth\\nIndia\\nJobs\\nKnowledge\\nLifestyle\\nScience\\nSpiritual\\nSports\\nTechnology\\nViral\\n\\n\\nAuto\\nScience\\nIndia\\nWorld\\nCrime\\nPolitics\\nAstrology\\nLatest News\\nKnowledge\\nOpinion / Analysis\\nShort Videos\\nViral\\nPhoto Gallery\\nVideos\\n\\nfollow us:\\n\\n\\n\\n\\n\\n\\nArtificial Intelligence News\\nKortix AI has launched Suna, the world’s first open-source general AI agent\\nKortix AI has unveiled Suna, the world’s first open-source general AI agent. Built for real-world tasks, it can automate research, write reports, run commands, and more — all from a chat-like interface. It works offline, is free to use, and is now available to host privately.\\n\\nSiddharth Shankar Published: 23 Apr 2025 13:22:PM\\n\\n\\nKortix AI launches Suna, the world’s first open-source AI agent for everyone\\nIt’s not every day that something drops into the AI world and genuinely feels like a shift. But that’s exactly what Kortix AI’s latest launch is aiming for. Called Suna, it claims to be the world’s first open-source “General AI Agent”, and it’s now live for anyone to use, inspect, or even self-host.\\nAnd unlike those big AI models hidden behind paywalls or enterprise APIs, Suna is out in the open. Anyone with basic tech know-how and a decent laptop with a GPU can run it locally. No monthly fees, no vendor lock-in.\\nAlso Read\\n\\nOpenAI eyes Chrome! What ChatGPT’s chief reveals at Google’s antitrust trial\\n\\nGoogle Gemini 2.5 Flash launched with reasoning AI, low cost, and developer controls\\n\\nSamsung and Motorola phones may integrate Perplexity AI as Assistant soon: report\\nAn AI that talks and acts\\nLet’s be clear: Suna isn’t your average chatbot. It’s more of a hands-on AI assistant that doesn’t just talk, but does stuff, things like scraping websites, creating documents, running commands, even deploying websites. It talks with APIs, runs browser automation, and has its own isolated Docker-based execution environment.\\nAll of this works through a friendly, chat-style interface built on React and Next.js. At the backend, Suna connects to OpenAI or Anthropic via LiteLLM, handles user data through Supabase, and even supports Redis for caching. In short, it’s a full-stack AI companion that can automate workflows, analyse markets, plan trips, and find LinkedIn profiles just by chatting with it.\\nReal-world use cases?\\nSuna is not just code and promises. Kortix has showcased nearly 20 real use cases to back it up. From writing market research reports and planning holidays to scraping Amazon reviews and generating B2B sales leads, it’s already doing the grunt work that normally takes hours.\\nOne task asked Suna to “find the best insurance policy for a house in Milan by scraping Italian websites.” Another had it scout for AI ethics speakers across Europe by checking YouTube and conference websites.\\nThe coolest part? It even generates games. One use case involved building a 90s-style spaceship game with alien enemies — all triggered by a simple prompt.\\nSelf-hosted and under your control\\nUnlike closed tools, Suna is built to be hosted privately. All you need is a Supabase project, Redis setup, and either an OpenAI or Anthropic API key. There’s support for Tavily (for better web search) and RapidAPI too. The setup instructions are detailed, but doable.\\nThe project runs on Apache 2.0 license, meaning it’s open for forking, tweaking, or deploying for internal company use.\\nThe vision behind Suna\\nKortix AI says Suna is built for “real-world tasks through natural conversation.” While OpenAI, Google, and others work on tightly-guarded AGI labs, this launch feels like a response from the indie world — showing that open AI agents aren’t just possible, they’re already here.\\nClick for more latest Artificial Intelligence news. Also get top headlines and latest news from India and around the world at News9.\\n\\nSiddharth Shankar\\nSiddharth Shankar brings over a decade of experience in the realm of journalism, film making, and marketing, currently heading the Technology, Science, Gaming, and Automobile sections. He blends his expertise and passion to bring forth cutting-edge stories that resonate with a dynamic audience. Siddharth co-founded BWAutoworld.com for BW Businessworld and was leading Science and Technology domains for TimesNow Digital. For leads Siddharth can be reached on Twitter - @Siddh4rth\\nRead More\\n\\nNews Technology News Artificial Intelligence News Kortix AI Has Launched Suna, The World’s First Open Source General AI Agent News\\n\\nLatest Stories\\n Pahalgam terror attack: Candlelight march in Pulwama against killings; locals denounce terror and call out Pakistan\\n IPL 2025: Jasprit Bumrah breaks Yuzvendra Chahal’s record to achieve stunning feat in T20s\\n IPL 2025: Rohit, Boult flourish as MI thrash SRH, jump to third spot with 4th straight win\\nNext Article\\nFollow us on social media\\nNews9live Channel\\n\\nUndo\\nU.S. Privacy\\nHas Pahalgam attack hit the heart of Centre’s normalcy pushDiscover\\nHubble captures image of glittering globular cluster M72Discover\\nSynocell | [Sponsored by Taboola](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=organic-video-reel-dvr:Video%20Reel%20Above%20Feed:)[Sponsored by Taboola](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=organic-video-reel-dvr:Video%20Reel%20Above%20Feed:) Learn More Joint Specialist Reveals: 7-Second Morning Routine Helps People Over 40 With PainRead MoreSkip\\nPanchgrahi Mahasanyog 2025: Major changes for 6 zodiac signsDiscover\\nRepo Rate cut will boost private consumption, investment: RBI GovernorDiscover\\nJoint Specialist Reveals: 7-Second Morning Routine Helps People Over 40 With PainThis could be the leading cause for joint pain and it has nothing to do with agingSynocell | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%201:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%201:) Learn More\\nUndo\\nExperts Agree—This Little Orange Shot Could Be The Answer To Your Slow DigestionDose | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%201:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%201:) Shop Now\\nUndo\\nSave Big On International Business-Class Tickets – Search DealsBusiness-Class Tickets | Search Ads | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%201:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%201:) Read More\\nUndo\\nHollywood Actress Leaks Weight Loss Trick, Gets Fired!Make this 30-second before bed to melt poundsslimhappynation | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%202:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%202:) Learn More\\nUndo\\nSeniors on SS Get New BenefitsRidiculous benefits seniors are entitled to in 2025, but often forget to claim.WalletJump | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%202:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%202:) Learn More\\nUndo\\nThe Morning Habit That's Silently Depleting Your Joint FluidJoints ache? Avoid this \\\"healthy\\\" habit and watch what happens...Arthrozene | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%203:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%203:) Learn More\\nUndo\\nMature Dating Starts TodayShare your feelings and emotions with someone.Secretmeet | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%203:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%203:) Read More\\nUndo\\nSearch For Senior StairliftsSee search results for Senior Stairlifts. View top options now.DealsSpirit | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%203:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%203:) Search Now\\nUndo\\nPeople Over 50 With Neuropathy Are in for a Big Surprise (It's Genius)Active Nexus | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%204:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%204:) Learn More\\nUndo\\nCalifornia: Gov Will Cover Your Cost To Install Solar Panels If You Live In These ZipsIf you pay more than $99/month for power, you probably qualify for this program.California Clean Energy | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%204:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%204:) Learn More\\nUndo\\nCardiologists: Want To Stay Slim at 58? Do This Turmeric Trick Every Morning!Make this 60 Second Before Bed to Melt Poundsslimhappynation | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%205:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%205:)\\nUndo\\nCalifornia: Gov Can Cover Your Cost To Install Solar Panels If You Live In These ZipsIf you pay more than $99/month for power, you probably qualify for this program.California Clean Energy | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%205:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%205:) Learn More\\nUndo\\nSide Sleepers Get Neck Pain - Few Know This Pillow Trickreadsleepdigest | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%205:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%205:)\\nUndo\\nSearch for Electric SUV Incentives and Rebates in Mountain ViewFind the right electric SUV for your lifestyle and budget.Electric SUVs | Yahoo Search | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%206:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%206:) Reserve\\nUndo\\nSeniors Born 1941-1979 Receive 55 Benefits This Month if They AskSeniorDealToday | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%206:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%206:)\\nUndo\\nPeople Over 45 With Neuropathy Are in for a Big Surprise (It's Genius)Active Nexus | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%207:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%207:)\\nUndo\\nAmazon Is Losing Money as California Shoppers Are Canceling Prime for This Clever HackThis simple trick can save tons of money on Amazon, but most Prime members are ignoring it.Online Shopping Tools | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%207:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%207:)\\nUndo\\nHere Is What Dental Implant For Seniors Above 60 will Cost You (Check Now)Dental Implants | Search Ads | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%207:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%207:) Read More\\nUndo\\nDating Site For Older Men - Meet Mature Ladies Now! DateMyAge | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%208:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%208:) Sign Up\\nUndo\\nWednesday: Congress' New Tax Bill To Forgive Millions in IRS Debt [Qualify Here]The White House announced a new plan to forgive over $300 million in taxpayer debt in 2025. If enacted, it would be the largest tax forgiveness windfall in American history.Fresh Start Information | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%208:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%208:) Learn More\\nUndo\\nFind Your Next Springtime Adventure in OregonSpring trails, rushing rivers, and blooming valleys—see it all with a guide.Travel Oregon | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%209:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%209:) Learn More\\nUndo\\nPopular Searches For Senior StairliftsSee search results for Senior Stairlifts. View top options now.DealsSpirit | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%209:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%209:) Search Now\\nUndo\\nWelcome Spring with an Oregon GetawaySpring trails, rushing rivers, and blooming valleys—see it all with a guide.Travel Oregon | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%209:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-b:Below%20Article%20Thumbnails%20|%20Card%209:) Learn More\\nUndo\\nSeniors In California May be Entitled To Benefits, But Often Forget To ClaimSenior Benefits And Discounts Are One Of The Few Truly Great Perks That Come With Getting OlderSeniorDealToday | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%2010:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%2010:)\\nUndo\\nCalifornia: New Windows Available If You Own A Home In These Zip CodesCheck your zip code now and see how to save thousands!Smart Lifestyle Trends | [Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%2010:)[Sponsored](https://popup.taboola.com/en/?template=colorbox&utm_source=tv9-news9live&utm_medium=referral&utm_content=thumbs-feed-01-a:Below%20Article%20Thumbnails%20|%20Card%2010:) Learn More\\nUndo\\nLatest News\\nMore\\nPahalgam terror attack: Candlelight march in Pulwama against killings; locals denounce terror and call out Pakistan\\n\\nIPL 2025: Jasprit Bumrah breaks Yuzvendra Chahal's record to achieve stunning feat in T20s\\n\\nIPL 2025: Rohit, Boult flourish as MI thrash SRH, jump to third spot with 4th straight win\\n\\nArshad Nadeem declines Neeraj Chopra's invite to compete in NC Classic javelin event\\n\\nMadras high court upholds ED raids on TASMAC\\n\\nPhoto Gallery\\nMore\\n\\nLove in Spain: 8 Romantic Getaways for every couple [PICS]\\nHappy Easter 2025: Vibrant images to celebrate the special Sunday\\n\\nCinematic couples we can’t get over – 8 iconic on-screen duos who still live in our hearts\\n\\nNavratri Day 3, Maa Chandraghanta images with wishes to share\\n\\nFrom PM Modi to movies: Day 1 highlights of TV9’s WITT 2025 Summit\\n\\nBack to Top\\n\\nNetwork\\nTV9Hindi.com\\nTV9Marathi.com\\nTV9Telugu.com\\nTV9Kannada.com\\nTV9Bangla.com\\nTV9Gujarati.com\\nTV9Punjabi.com\\nAssamTV9.com\\nMalayalamTV9.com\\nTv9tamilnews.com\\nTrends9.com\\nMoney9.com\\nNews9Plus.com\\n\\n\\nState & City News\\nAndhra Pradesh\\nBengaluru\\nDelhi\\nHyderabad\\nKarnataka\\nKerala\\nMumbai\\nTamil Nadu\\nTelangana\\n\\n\\nEntertainment\\nBigg Boss\\nBollywood\\nHollywood\\nMovie Reviews\\nOTT\\nRegional Cinema\\nTelevision\\n\\n\\nLifestyle\\nBeauty\\nFashion\\nFood & Drink\\nRelationships\\nSpirituality\\nTravel\\nFitness\\n\\n\\nSports\\nCricket\\nOlympics\\nUFC\\nWWE\\nKabaddi\\nFootball\\nTennis\\nOther Sports\\n\\n\\nPopular Categories\\nIndia\\nTechnology\\nAuto\\nEducation / Career\\nBusiness\\nViral\\nCrime\\nOpinion & Analysis\\nVideos\\nPhotos\\nWeb Stories\\n\\n\\n\\nTrending Topics\\n\\nLIVE TV\\nIPL 2025\\nCBSE\\nIndian Tigers and Tigresses\\nEx-Karnataka DGP Death\\nCanada Gurdwara Vandalism\\nKashmir Valley School Closed\\nAbhinav Shukla Threat\\nHoroscope Today\\nArtificial Intelligence\\nHindu Calendar 2025\\nMalayalam Calendar 2025\\nMarathi Calendar 2025\\nJoin News9 on WhatsApp\\n\\n\\nfollow us\\n\\n\\n\\n\\n\\n\\n\\n\\nContact us\\nAbout us\\nAdvertise with us\\nPrivacy & Cookies Notice\\nComplaint Redressal\\n\\nCopyright © 2025 News9 Live. All rights reserved\\n\"}, {\"type\": \"image\", \"image_url\": \"http://businessandsociety.org/wp-content/uploads/2021/04/image-from-rawpixel-id-267098-jpeg.jpg\", \"image_description\": \"A group of individuals is seated around a conference table, engaged in discussion, with colorful speech bubbles floating above them to represent communication and community activity.\"}, {\"type\": \"image\", \"image_url\": \"https://media.theresanaiforthat.com/lobe-ai.png\", \"image_description\": \"The content promotes Lobe, a tool that assists users in training machine learning models to identify plants, featuring a visual example of the Toyon plant and offering options to download or watch a tour.\"}, {\"type\": \"image\", \"image_url\": \"https://miro.medium.com/v2/resize:fit:1358/0*rkzJ0DQQFiuxj8i9.jpg\", \"image_description\": \"The diagram illustrates a self-querying process involving the construction and translation of a query about \\\"foo\\\" with a filter for the author \\\"bar\\\" before searching within a vector store.\"}, {\"type\": \"image\", \"image_url\": \"https://www.chaosgenius.io/blog/content/images/size/w1000/2024/06/Snowflake-AI-2.png\", \"image_description\": \"Cortex AI is highlighted, featuring sections for structured data management with \\\"Cortex Analyst,\\\" chat capabilities, document search with \\\"Cortex Search,\\\" and various model types including task-specific, foundation, and fine-tuned models from notable brands like Google and NVIDIA.\"}, {\"type\": \"image\", \"image_url\": \"https://3.files.edl.io/afc1/23/07/11/220259-c998abef-15de-4da7-9013-e696e0e0e184.png\", \"image_description\": \"A group of engaged students is seated at a table, actively discussing ideas while notebooks and a laptop are spread out before them in a classroom setting.\"}]", "tool_call_id": ""}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": "Based"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": " on the web search results:\n\n*   The GitHub activity page ([https://"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": "github.com/kortix-ai/suna/activity](https://"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": "github.com/kortix-ai/suna/activity)) allows for tracking contributions to the repository.\n*   An article on aisharenet"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": ".com ([https://www.aisharenet.com/en/suna/](https://www.aisharenet.com/en"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": "/suna/)) describes Suna as an open-source general-purpose AI agent that helps with tasks like web browsing, file management, data crawling, and website deployment. The article mentions the architecture includes a Python/FastAPI backend,"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": " a Next.js/React frontend, a Supabase database, and a Daytona sandbox.\n*   News9Live reports ([https://www.news9live.com/technology/artificial-intelligence/kortix-suna-open-"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": "source-ai-agent-launched-2842042](https://www.news9live.com/technology/artificial-intelligence/kortix-suna-open-source-ai-agent-launched-2842042)) that Kortix AI launched Suna as"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": " the world's first open-source general AI agent, capable of automating research, writing reports, and running commands through a chat interface. It also notes the Apache 2.0 license, enabling forking and modification. The article mentions real-world use cases and the ability to self-host the agent.\n\nConclusion:"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": "\n\nThe repository \"kortix-ai/suna\" is a trending open-source general AI agent. It supports functionalities such as web browsing, file management, and website deployment. It appears to have active community involvement.\n\nReferences:\n\n- [GitHub Activity Page](https://github.com/kortix-"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": "ai/suna/activity)\n\n- [Suna: Intelligent Agent with Integrated Browser Operations and Data ...](https://www.aisharenet.com/en/suna/)\n\n- [Kortix AI has launched Suna, the world's first open-source general AI ...](https://www"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-7b689bef-1312-4896-a1c6-1889fa797437", "role": "assistant", "content": ".news9live.com/technology/artificial-intelligence/kortix-suna-open-source-ai-agent-launched-2842042)\n", "finish_reason": "stop"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "content": "Okay, I understand"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "content": ". I will research the broader context and significance of the trending repository, determine its purpose"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "content": ", investigate its background, identify its industry/user base/application area, search for"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "content": " recent news, and identify its license and terms of use. I will track all sources and include a References section at the end.\n\n**Problem Statement**: Research"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "content": " the context and significance of a trending repository to understand its purpose, background, impact, and licensing.\n\n**Plan**: I will start by using the `get"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "content": "_github_trending_repositories` tool to find a trending repository. Then, I will use the `web_search_tool` to gather information about the repository's purpose, background, impact, and licensing. If necessary, I will"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "content": " use the `crawl_tool` to extract more detailed information from specific URLs found in the search results. Finally, I will synthesize the information and provide a structured report with a References section.\n\n**Execution:**\nFirst, I need to"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "content": " find a trending repository. I will start by searching for trending repositories in general, without specifying any language or time period.\n"}

event: tool_calls
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-503faf30-118c-4047-95bd-044b6a019ae0", "role": "assistant", "finish_reason": "tool_calls", "tool_calls": [{"name": "get_github_trending_repositories", "args": {}, "id": "", "type": "tool_call"}], "tool_call_chunks": [{"name": "get_github_trending_repositories", "args": "{}", "id": "", "index": null, "type": "tool_call_chunk"}]}

event: tool_call_result
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "2ad0b08b-09ed-42d2-8c37-5616dc1d6b8c", "role": "assistant", "content": "[\n  {\n    \"name\": \"suna\",\n    \"fullname\": \"kortix-ai/suna\",\n    \"url\": \"https://github.com/kortix-ai/suna\",\n    \"description\": \"\\n      Suna - Open Source Generalist AI Agent\\n    \",\n    \"language\": \"TypeScript\",\n    \"stars\": 4352,\n    \"forks\": 497,\n    \"current_period_stars\": 1105\n  },\n  {\n    \"name\": \"GPT-SoVITS\",\n    \"fullname\": \"RVC-Boss/GPT-SoVITS\",\n    \"url\": \"https://github.com/RVC-Boss/GPT-SoVITS\",\n    \"description\": \"\\n      1 min voice data can also be used to train a good TTS model! (few shot voice cloning)\\n    \",\n    \"language\": \"Python\",\n    \"stars\": 45237,\n    \"forks\": 5011,\n    \"current_period_stars\": 128\n  },\n  {\n    \"name\": \"Free-Certifications\",\n    \"fullname\": \"cloudcommunity/Free-Certifications\",\n    \"url\": \"https://github.com/cloudcommunity/Free-Certifications\",\n    \"description\": \"\\n      A curated list of free courses with certifications. Also available at https://free-certifications.com/\\n\",\n    \"language\": \"\",\n    \"stars\": 31414,\n    \"forks\": 2248,\n    \"current_period_stars\": 894\n  },\n  {\n    \"name\": \"UI-TARS\",\n    \"fullname\": \"bytedance/UI-TARS\",\n    \"url\": \"https://github.com/bytedance/UI-TARS\",\n    \"description\": \"\",\n    \"language\": \"Python\",\n    \"stars\": 5024,\n    \"forks\": 340,\n    \"current_period_stars\": 410\n  },\n  {\n    \"name\": \"generative-ai-for-beginners\",\n    \"fullname\": \"microsoft/generative-ai-for-beginners\",\n    \"url\": \"https://github.com/microsoft/generative-ai-for-beginners\",\n    \"description\": \"\\n      21 Lessons, Get Started Building with Generative AI \\ud83d\\udd17 https://microsoft.github.io/generative-ai-for-beginners/\\n\",\n    \"language\": \"Jupyter Notebook\",\n    \"stars\": 79617,\n    \"forks\": 41214,\n    \"current_period_stars\": 212\n  },\n  {\n    \"name\": \"leaked-system-prompts\",\n    \"fullname\": \"jujumilk3/leaked-system-prompts\",\n    \"url\": \"https://github.com/jujumilk3/leaked-system-prompts\",\n    \"description\": \"\\n      Collection of leaked system prompts\\n    \",\n    \"language\": \"\",\n    \"stars\": 3049,\n    \"forks\": 401,\n    \"current_period_stars\": 55\n  },\n  {\n    \"name\": \"khoj\",\n    \"fullname\": \"khoj-ai/khoj\",\n    \"url\": \"https://github.com/khoj-ai/khoj\",\n    \"description\": \"\\n      Your AI second brain. Self-hostable. Get answers from the web or your docs. Build custom agents, schedule automations, do deep research. Turn any online or local LLM into your personal, autonomous AI (gpt, claude, gemini, llama, qwen, mistral). Get started - free.\\n    \",\n    \"language\": \"Python\",\n    \"stars\": 29113,\n    \"forks\": 1622,\n    \"current_period_stars\": 42\n  },\n  {\n    \"name\": \"dinov2\",\n    \"fullname\": \"facebookresearch/dinov2\",\n    \"url\": \"https://github.com/facebookresearch/dinov2\",\n    \"description\": \"\\n      PyTorch code and models for the DINOv2 self-supervised learning method.\\n    \",\n    \"language\": \"Jupyter Notebook\",\n    \"stars\": 10433,\n    \"forks\": 940,\n    \"current_period_stars\": 8\n  },\n  {\n    \"name\": \"tensorflow\",\n    \"fullname\": \"tensorflow/tensorflow\",\n    \"url\": \"https://github.com/tensorflow/tensorflow\",\n    \"description\": \"\\n      An Open Source Machine Learning Framework for Everyone\\n    \",\n    \"language\": \"C++\",\n    \"stars\": 189614,\n    \"forks\": 74652,\n    \"current_period_stars\": 43\n  }\n]", "tool_call_id": ""}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-6cd23790-3925-4ada-a240-f29d967298e4", "role": "assistant", "content": "Okay, I have"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-6cd23790-3925-4ada-a240-f29d967298e4", "role": "assistant", "content": " a list of trending repositories. I will choose \"kortix-ai/suna\" to"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-6cd23790-3925-4ada-a240-f29d967298e4", "role": "assistant", "content": " research further.\n\nNow I will use the `web_search_tool"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-6cd23790-3925-4ada-a240-f29d967298e4", "role": "assistant", "content": "` to find information about the \"kortix-ai/suna\" repository.\n"}

event: tool_calls
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-6cd23790-3925-4ada-a240-f29d967298e4", "role": "assistant", "finish_reason": "tool_calls", "tool_calls": [{"name": "web_search", "args": {"query": "kortix-ai/suna repository"}, "id": "", "type": "tool_call"}], "tool_call_chunks": [{"name": "web_search", "args": "{\"query\":\"kortix-ai/suna repository\"}", "id": "", "index": null, "type": "tool_call_chunk"}]}

event: tool_call_result
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "bf208b0c-1dd6-4ae8-8b30-bc1369ec4ed0", "role": "assistant", "content": "[{\"type\": \"page\", \"title\": \"Suna: Intelligent Agent with Integrated Browser Operations and Data ...\", \"url\": \"https://www.aisharenet.com/en/suna/\", \"content\": \"Suna is an open source general-purpose AI agent developed by Kortix AI, hosted on GitHub, based on the Apache 2.0 license, allowing users to download, modify and self-host it for free. It helps users with complex tasks such as web browsing, file management, data crawling, and website deployment through natural language conversations.Suna is built on a modular architecture that includes a Python/FastAPI backend, a Next.js/React frontend, a Supabase database, and a Daytona sandbox to ensure\", \"score\": 0.84948516, \"raw_content\": \"Suna: Intelligent Agents for Integrated Browser Operations and Data Analytics\\n\\nGeneral Introduction\\n\\nSuna is an open source general-purpose AI agent developed by Kortix AI, hosted on GitHub, based on the Apache 2.0 license, allowing users to download, modify and self-host it for free. It helps users with complex tasks such as web browsing, file management, data crawling, and website deployment through natural language conversations.Suna is built on a modular architecture that includes a Python/FastAPI backend, a Next.js/React frontend, a Supabase database, and a Daytona sandbox to ensure security and flexibility. It supports integration with large language models such as OpenAI, Anthropic, and more, with Tavily and RapidAPI provide real-time search and data services.\\n\\n\\n\\n\\n\\nFunction List\\n\\n\\n\\nUsing Help\\n\\nInstallation process\\n\\nSuna supports self-hosting and is suitable for users with Python and Node.js development experience. Here are the detailed installation steps:\\n\\nMain Functions\\n\\nBrowser Automation\\n\\nSuna uses the Chromium browser to perform web actions. For example, type \\\"Extract iPhone 14 price from Amazon\\\":\\n\\nDocument management\\n\\nSuna supports file creation and editing. For example, \\\"Create report.txt to write sales data\\\":\\n\\nWeb crawling and searching\\n\\nSuna integrates with Tavily to provide real-time search. For example, \\\"Analyzing the German automotive market\\\":\\n\\ncommand-line operation\\n\\nSuna runs Linux commands in the Daytona sandbox. For example, \\\"list catalog files\\\":\\n\\nWebsite deployment\\n\\nSuna supports static website deployment. For example, \\\"Deploy my HTML project\\\":\\n\\nFeatured Function Operation\\n\\nMission planning\\n\\nSuna use todo.md Systematically break down tasks. For example, \\\"Plan a 10-day trip to London.\\\"\\n\\nAPI Integration\\n\\nSuna supports APIs for LinkedIn, Zillow, etc. e.g. \\\"Grab Barcelona company data\\\":\\n\\n\\n\\napplication scenario\\n\\n\\n\\nQA\\n\\nRelated articles\\n\\nRecommended\\n\\nCan't find AI tools? Try here!\\n\\nJust type in the keyword Accessibility Bing SearchYou can quickly find all the AI tools on this site.\\n\\nAI Programming IDE\\n\\nAI Digital Human Assistant\\n\\nIntelligent Writing Tools\\n\\nAI Video Generation\\n\\nE-commerce Poster Generation\\n\\nSmart PPT Tools\\n\\nFLUX.1 image generator (supports Chinese input)\\n\\nRecent AI Hotspots\\n\\nAI Tools Recommendations\\n\\nAI Tools Classification\\n\\n© 2022-2025   Chief AI Sharing Circle   \\n\\nAI News\\nAI tools\\nAI utility commands\\nAI hands-on tutorials\\nAI knowledge\\n\\n\\nSite Map | 京ICP备**********号-1\\n\\nAI News\\n\\nAI tools\\n\\nAI utility commands\\n\\nAI hands-on tutorials\\n\\nAI knowledge\\n\\nSite Map | 京ICP备**********号-1\\n\\n\"}, {\"type\": \"page\", \"title\": \"suna/.gitignore at main · kortix-ai/suna - GitHub\", \"url\": \"https://github.com/kortix-ai/suna/blob/main/.gitignore\", \"content\": \"Suna - Open Source Generalist AI Agent. Contribute to kortix-ai/suna development by creating an account on GitHub.\", \"score\": 0.8315952}, {\"type\": \"page\", \"title\": \"suna/backend/agent/prompt.py at main · kortix-ai/suna - GitHub\", \"url\": \"https://github.com/kortix-ai/suna/blob/main/backend/agent/prompt.py\", \"content\": \"Suna - Open Source Generalist AI Agent. Contribute to kortix-ai/suna development by creating an account on GitHub.\", \"score\": 0.********}, {\"type\": \"image\", \"image_url\": \"https://www.suna.so/thumbnail-dark.png\", \"image_description\": \"The design features the names \\\"Kortix AI\\\" and \\\"Suna\\\" displayed prominently in white against a black background, accompanied by a stylized symbol, creating a modern and sleek visual representation.\"}, {\"type\": \"image\", \"image_url\": \"https://repository-images.githubusercontent.com/*********/c12ebf0e-b8aa-4050-ba6a-e4edc7b999a6\", \"image_description\": \"The interface features the title \\\"Suna, the AI Employee\\\" with a description indicating that Suna is a generalist AI agent by Kortix that acts on behalf of users, accompanied by a prompt for user interaction.\"}, {\"type\": \"image\", \"image_url\": \"https://assets-global.website-files.com/65d92278c643d1d4d3f5a32d/66005e58c835756ac2380ce3_kortix-ai-logo-black.png\", \"image_description\": \"The logo of kortix.ai is displayed prominently, featuring a geometric design alongside the text in a modern font.\"}, {\"type\": \"image\", \"image_url\": \"https://assets.aitools.fyi/cdn-cgi/image/width=3840/https://assets.aitools.fyi/ts/14395.jpg\", \"image_description\": \"The content features a webpage promoting Suna, an AI agent by Kortix, highlighting its capabilities as a generalist AI employee and offering options like \\\"Ask Suna to...\\\" along with navigation links for various use cases and open-source information.\"}, {\"type\": \"image\", \"image_url\": \"https://miro.medium.com/v2/resize:fit:1024/1*b2hDJZiQHsTS22FlRlJEUA.jpeg\", \"image_description\": \"A humanoid figure holds a stack of colorful books in a vibrant, abstract library filled with rows of shelves.\"}]", "tool_call_id": ""}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "**"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "Research Findings**\n\n*   **Purpose and Function**: Suna is an open-"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "source, general-purpose AI agent developed by Kortix AI. It is designed to assist"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": " users with complex tasks through natural language conversations, including web browsing, file management, data crawling, and website deployment. [Suna: Intelligent Agent with Integrated Browser Operations"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": " and Data ...](https://www.aisharenet.com/en/suna/)\n*   **Background and Architecture**: Suna is built on a modular"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": " architecture consisting of a Python/FastAPI backend, a Next.js/React frontend, a Supabase database, and a Daytona sandbox for security. It supports integration with large language models like OpenAI and Anthropic and utilizes Tavily and RapidAPI for real"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "-time search and data services. [Suna: Intelligent Agent with Integrated Browser Operations and Data ...](https://www.aisharenet.com/en/suna/)\n*   **Features**: Suna offers several functions"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": ", including browser automation (using Chromium), document management, web crawling and searching (integrated with Tavily), command-line operation (in a Daytona sandbox), and static website deployment. It also supports mission planning and API integration for services like LinkedIn and Zillow. [Suna: Intelligent Agent with Integrated Browser Operations and Data ..."}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "](https://www.aisharenet.com/en/suna/)\n*   **License**: Suna is based on the Apache 2.0 license, which allows users to download, modify, and self-host it for free. [Suna: Intelligent Agent with Integrated Browser Operations and Data ..."}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "](https://www.aisharenet.com/en/suna/)\n\n**Conclusion**\n\nSuna is an open-source AI agent developed by Kortix AI with the goal of providing a versatile tool for automating tasks such as web browsing, data management, and website deployment. Its modular architecture and support"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": " for various integrations make it a flexible solution for users with Python and Node.js development experience. The Apache 2.0 license allows for free use, modification, and self-hosting.\n\n**References**\n\n- [Suna: Intelligent Agent with Integrated Browser Operations and Data ...](https://www.aisharenet."}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "com/en/suna/)\n\n- [suna/.gitignore at main · kortix-ai/suna - GitHub](https://github.com/kortix-ai/suna/blob/main/.gitignore)\n\n- [suna/backend/agent/prompt.py at main · kort"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "ix-ai/suna - GitHub](https://github.com/kortix-ai/suna/blob/main/backend/agent/prompt.py)\n\n**Images**\n\n![Kortix AI and Suna Logo](https://www.suna.so/thumbnail-dark.png)"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "\n\n![Suna, the AI Employee](https://repository-images.githubusercontent.com/*********/c12ebf0e-b8aa-4050-ba6a-e4edc7b999a6)\n\n"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "![kortix.ai logo](https://assets-global.website-files.com/65d92278c643d1d4d3f5a32d/66005e58c835756ac2380ce3"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "_kortix-ai-logo-black.png)\n\n![Suna AI Agent](https://assets.aitools.fyi/cdn-cgi/image/width=3840/https://assets.aitools.fyi/ts/14395.jpg)\n\n!["}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "researcher", "id": "run-2af9f798-519c-487a-bbce-4794c05b7c89", "role": "assistant", "content": "Abstract Library](https://miro.medium.com/v2/resize:fit:1024/1*b2hDJZiQHsTS22FlRlJEUA.jpeg)\n", "finish_reason": "stop"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "#"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " Top Trending GitHub"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " Repository Today: kortix-ai/suna\n\n## Key Points\n\n*"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "   The top trending repository on GitHub today is `kortix-ai/suna`,"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " an open-source general AI agent.\n*   `suna` is developed by Kortix AI and is written in TypeScript.\n*   The repository"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " is trending due to gaining 1105 new stars today.\n*   `suna` assists with tasks like web browsing, file management, data crawling, and"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " website deployment, and allows automating research, writing reports, and running commands through a chat interface.\n*   `suna` is built on a modular architecture consisting of a Python/FastAPI backend, a Next.js/React frontend, a"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " Supabase database, and a Daytona sandbox.\n*   It uses the Apache 2.0 license, which permits users to freely download, modify, and self-host the agent.\n\n---\n\n## Overview\n\nThis report provides an overview of the"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " top trending repository on GitHub today, `kortix-ai/suna`. This repository is an open-source general AI agent designed to assist users with various tasks through natural language conversations. The report includes details about the repository's purpose, architecture, features, and licensing, providing a comprehensive look at why it is currently trending"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " on GitHub.\n\n---\n\n## Detailed Analysis\n\n### Basic Information\n\n*   **Repository Name:** suna\n*   **Owner/Organization:** kortix-ai\n*   **Description:** Suna - Open Source Generalist AI Agent\n*   **Primary Language:** TypeScript\n*   **Trending Reason:** 11"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "05 new stars today\n\n### Purpose and Functionality\n\n`suna` is designed as a general-purpose AI agent that can assist users with complex tasks. It allows for automating research, writing reports, and running commands through a chat interface. Key functionalities include:\n\n*   Web browsing\n*   File management\n"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "*   Data crawling\n*   Website deployment\n\n### Architecture and Technology\n\n`suna` is built on a modular architecture that includes:\n\n*   **Backend:** Python/FastAPI\n*   **Frontend:** Next.js/React\n*   **Database:** Supabase\n*   **Sandbox:** Daytona"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "\n\nIt supports integration with large language models like OpenAI and Anthropic, utilizing Tavily and RapidAPI for real-time search and data services.\n\n### Features\n\n`suna` provides a range of features, including:\n\n*   Browser automation (using Chromium)\n*   Document management\n*   Web crawling and searching"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " (integrated with Tavily)\n*   Command-line operation (in a Daytona sandbox)\n*   Static website deployment\n*   Mission planning\n*   API integration for services like LinkedIn and Zillow\n\n### License\n\n`suna` is licensed under the Apache 2.0 license, allowing users to freely"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " download, modify, and self-host it.\n\n---\n\n### Images\n\n![Kortix AI and Suna Logo](https://www.suna.so/thumbnail-dark.png)\n\n![Suna, the AI Employee](https://repository-images.githubusercontent.com/8681"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "73144/c12ebf0e-b8aa-4050-ba6a-e4edc7b999a6)\n\n![kortix.ai logo](https://assets-global.website-files.com/65d92"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "278c643d1d4d3f5a32d/66005e58c835756ac2380ce3_kortix-ai-logo-black.png)\n\n![Suna AI Agent](https://assets"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": ".aitools.fyi/cdn-cgi/image/width=3840/https://assets.aitools.fyi/ts/14395.jpg)\n\n![Abstract Library](https://miro.medium.com/v2/resize:fit:102"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "4/1*b2hDJZiQHsTS22FlRlJEUA.jpeg)\n\n---\n\n## Key Citations\n\n- [Trending Repositories on GitHub](https://github.com/kortix-ai/suna)\n\n- [GitHub Activity Page](https://github.com"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "/kortix-ai/suna/activity)\n\n- [Suna: Intelligent Agent with Integrated Browser Operations and Data ...](https://www.aisharenet.com/en/suna/)\n\n- [Kortix AI has launched Suna, the world's first open-source general"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": " AI ...](https://www.news9live.com/technology/artificial-intelligence/kortix-suna-open-source-ai-agent-launched-2842042)\n\n- [suna/.gitignore at main · kortix-ai/suna - GitHub](https://github."}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "com/kortix-ai/suna/blob/main/.gitignore)\n\n- [suna/backend/agent/prompt.py at main · kortix-ai/suna - GitHub](https://github.com/kortix-ai/suna/blob/main/backend/agent/"}

event: message_chunk
data: {"thread_id": "LmC3xxJCFljoFXggnmvst", "agent": "reporter", "id": "run-a0a0a9e5-201f-4ee8-aa5f-71ad07a2db13", "role": "assistant", "content": "prompt.py)\n", "finish_reason": "stop"}
